import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateMessageDto } from './dto/update-message.dto';
import { GetMessagesDto } from './dto/get-messages.dto';
import { VotePollDto, JoinLuckyDrawDto, DrawLuckyDrawDto, RollDiceDto, UpdateSplitPaymentDto } from './dto/vote-poll.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiResponseDto } from '../../common/dto/api-response.dto';
import { TodoService } from '../todo/todo.service';
import { UpdateTodoDto } from '../todo/dto/update-todo.dto';

@ApiTags('chat')
@Controller()
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly chatGateway: ChatGateway,
    private readonly todoService: TodoService,
  ) {}

  @Post('spaces/:spaceId/messages')
  @ApiOperation({ summary: 'Send a message in a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendMessage(
    @Param('spaceId') spaceId: string,
    @Body() createMessageDto: CreateMessageDto,
    @Request() req,
  ): Promise<ApiResponseDto> {
    const message = await this.chatService.createMessage(
      spaceId,
      req.user.id,
      createMessageDto,
    );

    // For invite-only lucky draws, broadcast the message update since participants were auto-added
    if (createMessageDto.type === 'luckydraw' &&
        createMessageDto.metadata?.isInviteOnly &&
        createMessageDto.metadata?.invitedUserIds?.length > 0) {
      // Broadcast the message with updated participants to all users in the space
      this.chatGateway.server.to(`space:${spaceId}`).emit('message-updated', {
        action: 'updated',
        messageId: message.id,
        message: message,
      });
    }

    return {
      success: true,
      data: message,
      message: 'Message sent successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('spaces/:spaceId/messages')
  @ApiOperation({ summary: 'Get messages in a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 200, description: 'Messages retrieved successfully' })
  async getMessages(
    @Param('spaceId') spaceId: string,
    @Query() getMessagesDto: GetMessagesDto,
    @Request() req,
  ): Promise<ApiResponseDto> {
    const result = await this.chatService.getMessages(
      spaceId,
      req.user.id,
      getMessagesDto,
    );

    return {
      success: true,
      data: result,
      message: 'Messages retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('spaces/:spaceId/activities')
  @ApiOperation({ summary: 'Get activity messages in a space (polls, dice, lucky draws, location)' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 200, description: 'Activities retrieved successfully' })
  async getActivities(
    @Param('spaceId') spaceId: string,
    @Query() getMessagesDto: GetMessagesDto,
    @Request() req,
  ): Promise<ApiResponseDto> {
    // Set activity types filter
    const activityDto = {
      ...getMessagesDto,
      types: 'poll,dice,luckydraw,location'
    };

    const result = await this.chatService.getMessages(
      spaceId,
      req.user.id,
      activityDto,
    );

    return {
      success: true,
      data: result,
      message: 'Activities retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }



  @Put('messages/:messageId')
  @ApiOperation({ summary: 'Edit a message' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({ status: 200, description: 'Message updated successfully' })
  async updateMessage(
    @Param('messageId') messageId: string,
    @Body() updateMessageDto: UpdateMessageDto,
    @Request() req,
  ): Promise<ApiResponseDto> {
    const message = await this.chatService.updateMessage(
      messageId,
      req.user.id,
      updateMessageDto,
    );

    return {
      success: true,
      data: message,
      message: 'Message updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('messages/:messageId')
  @ApiOperation({ summary: 'Delete a message' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({ status: 200, description: 'Message deleted successfully' })
  async deleteMessage(
    @Param('messageId') messageId: string,
    @Request() req,
  ): Promise<ApiResponseDto> {
    await this.chatService.deleteMessage(messageId, req.user.id);

    return {
      success: true,
      data: null,
      message: 'Message deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('messages/:messageId')
  @ApiOperation({ summary: 'Get a specific message' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({ status: 200, description: 'Message retrieved successfully' })
  async getMessage(
    @Param('messageId') messageId: string,
    @Request() req,
  ): Promise<ApiResponseDto> {
    const message = await this.chatService.getMessageById(messageId, req.user.id);

    return {
      success: true,
      data: message,
      message: 'Message retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('messages/:messageId/vote')
  @ApiOperation({ summary: 'Vote on a poll' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Vote submitted successfully',
    type: ApiResponseDto,
  })
  async votePoll(
    @Param('messageId') messageId: string,
    @Body() votePollDto: VotePollDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.chatService.votePoll(messageId, req.user.id, votePollDto.optionIds);

    // Get the updated message to broadcast
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${updatedMessage.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: null,
      message: 'Vote submitted successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('messages/:messageId/poll')
  @ApiOperation({ summary: 'Get poll results' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Poll results retrieved successfully',
    type: ApiResponseDto,
  })
  async getPollResults(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const pollData = await this.chatService.getPollResults(messageId, req.user.id);

    return {
      success: true,
      data: pollData,
      message: 'Poll results retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('messages/:messageId/lucky-draw/join')
  @ApiOperation({ summary: 'Join a lucky draw' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully joined lucky draw',
    type: ApiResponseDto,
  })
  async joinLuckyDraw(
    @Param('messageId') messageId: string,
    @Body() joinLuckyDrawDto: JoinLuckyDrawDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const updatedLuckyDraw = await this.chatService.joinLuckyDraw(messageId, req.user.id);

    // Get the updated message to broadcast
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${updatedMessage.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: updatedLuckyDraw,
      message: 'Successfully joined lucky draw',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('messages/:messageId/lucky-draw/draw')
  @ApiOperation({ summary: 'Perform lucky draw' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Lucky draw performed successfully',
    type: ApiResponseDto,
  })
  async performLuckyDraw(
    @Param('messageId') messageId: string,
    @Body() drawLuckyDrawDto: DrawLuckyDrawDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const drawResult = await this.chatService.performLuckyDraw(messageId, req.user.id);

    // Get the updated message to broadcast
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${updatedMessage.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: drawResult,
      message: 'Lucky draw performed successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('messages/:messageId/lucky-draw')
  @ApiOperation({ summary: 'Get lucky draw results' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Lucky draw results retrieved successfully',
    type: ApiResponseDto,
  })
  async getLuckyDrawResults(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const luckyDrawData = await this.chatService.getLuckyDrawResults(messageId, req.user.id);

    return {
      success: true,
      data: luckyDrawData,
      message: 'Lucky draw results retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('messages/:messageId/roll-dice')
  @ApiOperation({ summary: 'Roll dice and update results' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Dice rolled successfully',
    type: ApiResponseDto,
  })
  async rollDice(
    @Param('messageId') messageId: string,
    @Body() rollDiceDto: RollDiceDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const updatedDice = await this.chatService.rollDice(messageId, req.user.id, rollDiceDto.diceData);

    // Get the updated message to broadcast
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${updatedMessage.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: updatedDice,
      message: 'Dice rolled successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('messages/:messageId/dice')
  @ApiOperation({ summary: 'Get dice results' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Dice results retrieved successfully',
    type: ApiResponseDto,
  })
  async getDiceResults(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const diceData = await this.chatService.getDiceResults(messageId, req.user.id);

    return {
      success: true,
      data: diceData,
      message: 'Dice results retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Patch('messages/:messageId/split-payment')
  @ApiOperation({ summary: 'Update split payment status' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Split payment status updated successfully',
    type: ApiResponseDto,
  })
  async updateSplitPaymentStatus(
    @Param('messageId') messageId: string,
    @Body() updateSplitPaymentDto: UpdateSplitPaymentDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.chatService.updateSplitPaymentStatus(
      messageId,
      req.user.id,
      updateSplitPaymentDto.participantId,
      updateSplitPaymentDto.isPaid,
    );

    // Get the updated message to broadcast
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${updatedMessage.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: null,
      message: 'Split payment status updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('spaces/:spaceId/settle-debts')
  @ApiOperation({ summary: 'Settle all debts between two users' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 200, description: 'Debts settled successfully' })
  async settleDebts(
    @Param('spaceId') spaceId: string,
    @Body() settleDebtsDto: { fromUserId: string; toUserId: string },
    @Request() req,
  ): Promise<ApiResponseDto> {
    const result = await this.chatService.settleDebts(
      spaceId,
      settleDebtsDto.fromUserId,
      settleDebtsDto.toUserId,
      req.user.id,
    );

    return {
      success: true,
      data: result,
      message: 'Debts settled successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put('messages/:messageId/todo')
  @ApiOperation({ summary: 'Update todo status from message' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Todo message not found',
  })
  async updateTodoFromMessage(
    @Param('messageId') messageId: string,
    @Body() updateTodoDto: UpdateTodoDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    try {
      // First get the message to find the associated todo
      const message = await this.chatService.getMessageById(messageId, req.user.id);

      if (!message || !message.todo) {
        throw new NotFoundException('Todo message not found');
      }

      const todo = await this.todoService.updateTodo(
        message.todo.id,
        req.user.id,
        updateTodoDto,
      );

      // Broadcast the updated message to all users in the space
      this.chatGateway.server.to(`space:${message.spaceId}`).emit('message-updated', {
        action: 'updated',
        messageId: messageId,
        message: message,
      });

      return {
        success: true,
        data: todo,
        message: 'Todo updated successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // Handle constraint conflicts gracefully
      if (error.message?.includes('UNIQUE constraint failed')) {
        throw new ConflictException('Operation conflict detected. Please try again.');
      }
      throw error;
    }
  }

  @Post('messages/:messageId/todo/complete')
  @ApiOperation({ summary: 'Mark todo as completed from message' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo marked as completed',
  })
  async completeTodoFromMessage(
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    // First get the message to find the associated todo
    const message = await this.chatService.getMessageById(messageId, req.user.id);

    if (!message || !message.todo) {
      throw new NotFoundException('Todo message not found');
    }

    const todo = await this.todoService.completeTodo(
      message.todo.id,
      req.user.id,
    );

    // Update the message metadata to reflect the completion
    await this.chatService.updateMessage(messageId, req.user.id, {
      content: message.content, // Keep the same content
    });

    // Get the updated message
    const updatedMessage = await this.chatService.getMessageById(messageId, req.user.id);

    // Broadcast the updated message to all users in the space
    this.chatGateway.server.to(`space:${message.spaceId}`).emit('message-updated', {
      action: 'updated',
      messageId: messageId,
      message: updatedMessage,
    });

    return {
      success: true,
      data: todo,
      message: 'Todo marked as completed',
      timestamp: new Date().toISOString(),
    };
  }
}
