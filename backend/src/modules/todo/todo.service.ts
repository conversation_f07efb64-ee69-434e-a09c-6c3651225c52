import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, In, <PERSON>Than, <PERSON><PERSON>han } from 'typeorm';
import { Todo, TodoStatus } from '../../entities/todo.entity';
import { TodoParticipant, TodoParticipantRole } from '../../entities/todo-participant.entity';
import { TodoTag } from '../../entities/todo-tag.entity';
import { Tag } from '../../entities/tag.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { Message, MessageType } from '../../entities/message.entity';
import { User } from '../../entities/user.entity';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto, CompleteTodoDto } from './dto/update-todo.dto';
import { GetTodosDto } from './dto/get-todos.dto';
import { ChatGateway } from '../chat/chat.gateway';
import { TagService } from '../tag/tag.service';


@Injectable()
export class TodoService {
  constructor(
    @InjectRepository(Todo)
    private todoRepository: Repository<Todo>,
    @InjectRepository(TodoParticipant)
    private todoParticipantRepository: Repository<TodoParticipant>,
    @InjectRepository(TodoTag)
    private todoTagRepository: Repository<TodoTag>,
    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @Inject(forwardRef(() => ChatGateway))
    private chatGateway: ChatGateway,
    @Inject(forwardRef(() => TagService))
    private tagService: TagService,

  ) {}

  async createTodo(
    spaceId: string,
    userId: string,
    createTodoDto: CreateTodoDto,
  ): Promise<Todo> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    // Determine initial status based on due date
    const initialStatus = createTodoDto.dueDate ? TodoStatus.IN_PROGRESS : TodoStatus.PENDING;

    // Create a chat message for this TODO if not created from a message
    let messageId = createTodoDto.messageId;
    if (!messageId) {
      // Create TODO metadata for the message
      const todoMetadata = {
        title: createTodoDto.title,
        details: createTodoDto.details,
        dueDate: createTodoDto.dueDate,
        dueTime: createTodoDto.dueTime,
        location: createTodoDto.location,
        // Note: Tags are now managed through the unified Tag system
        participants: createTodoDto.participants || [],
        creatorId: userId,
      };

      // Create the message
      const message = this.messageRepository.create({
        spaceId,
        senderId: userId,
        type: MessageType.TODO,
        content: createTodoDto.title,
        metadata: todoMetadata,
      });

      const savedMessage = await this.messageRepository.save(message);
      messageId = savedMessage.id;
    }

    // Create the todo
    const todo = this.todoRepository.create({
      spaceId,
      creatorId: userId,
      messageId: messageId,
      title: createTodoDto.title,
      details: createTodoDto.details,
      dueDate: createTodoDto.dueDate ? new Date(createTodoDto.dueDate) : undefined,
      dueTime: createTodoDto.dueTime,
      location: createTodoDto.location,
      status: initialStatus,
    });

    const savedTodo = await this.todoRepository.save(todo);

    // Handle tags if provided
    if (createTodoDto.tags && createTodoDto.tags.length > 0) {
      try {
        const tags = await this.tagService.getOrCreateTags(
          spaceId,
          userId,
          createTodoDto.tags,
        );

        // Create TodoTag associations
        const todoTags = tags.map(tag =>
          this.todoTagRepository.create({
            todoId: savedTodo.id,
            tagId: tag.id,
          })
        );

        await this.todoTagRepository.save(todoTags);
      } catch (error) {
        console.error('❌ Error processing tags:', error);
        throw error;
      }
    }

    // Create participants if provided
    if (createTodoDto.participants && createTodoDto.participants.length > 0) {
      const participants = [];

      for (const participantData of createTodoDto.participants) {
        // Get user information to populate userName if not provided
        const user = await this.userRepository.findOne({
          where: { id: participantData.userId },
        });

        if (user) {
          participants.push(
            this.todoParticipantRepository.create({
              todoId: savedTodo.id,
              userId: participantData.userId,
              userName: participantData.userName || user.displayName || `User ${participantData.userId}`,
              role: participantData.role,
              isNotified: participantData.isNotified,
            })
          );
        }
      }

      if (participants.length > 0) {
        await this.todoParticipantRepository.save(participants);
      }
    }

    // Return the todo with participants and tags
    const todoWithRelations = await this.todoRepository.findOne({
      where: { id: savedTodo.id },
      relations: ['creator', 'participants', 'participants.user', 'completedByUser', 'todoTags', 'todoTags.tag'],
    });

    // If we created a new message, broadcast it to the chat
    if (!createTodoDto.messageId) {
      const messageWithSender = await this.messageRepository.findOne({
        where: { id: messageId },
        relations: ['sender'],
      });

      if (messageWithSender) {
        // Broadcast the new message to the chat
        this.chatGateway.server.to(`space:${spaceId}`).emit('new-message', {
          spaceId,
          message: messageWithSender,
        });
      }
    }

    // Broadcast todo creation via WebSocket
    this.chatGateway.broadcastTodoCreated(spaceId, todoWithRelations);

    return todoWithRelations;
  }

  // Note: Space tags are now managed through the unified Tag system
  // Use the Tag API endpoints instead

  async getTodos(
    spaceId: string,
    userId: string,
    getTodosDto: GetTodosDto,
  ): Promise<{ todos: Todo[]; total: number }> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    const {
      limit,
      offset,
      status,
      tags,
      dueBefore,
      dueAfter,
      createdBefore,
      createdAfter,
      creatorId,
      participantId,
      search,
      sortBy,
      sortOrder,
    } = getTodosDto;

    const whereConditions: any = { spaceId };

    // Filter by status
    if (status) {
      const statusArray = status.split(',').map(s => s.trim());
      whereConditions.status = In(statusArray);
    }

    // Filter by due date
    if (dueBefore) {
      whereConditions.dueDate = LessThan(new Date(dueBefore));
    }
    if (dueAfter) {
      whereConditions.dueDate = MoreThan(new Date(dueAfter));
    }

    // Filter by creation date
    if (createdBefore) {
      whereConditions.createdAt = LessThan(new Date(createdBefore));
    }
    if (createdAfter) {
      whereConditions.createdAt = MoreThan(new Date(createdAfter));
    }

    // Filter by creator
    if (creatorId) {
      whereConditions.creatorId = creatorId;
    }

    // Search in title and details
    if (search) {
      whereConditions.title = Like(`%${search}%`);
    }

    const findOptions: FindManyOptions<Todo> = {
      where: whereConditions,
      relations: ['creator', 'participants', 'participants.user', 'completedByUser', 'todoTags', 'todoTags.tag'],
      order: { [sortBy]: sortOrder },
      take: limit,
      skip: offset,
    };

    let [todos, total] = await this.todoRepository.findAndCount(findOptions);

    // Filter by tags if specified
    if (tags) {
      const tagArray = tags.split(',').map(t => t.trim());

      // Get todos that have any of the specified tags
      const todosWithTags = await this.todoRepository
        .createQueryBuilder('todo')
        .innerJoin('todo.todoTags', 'todoTag')
        .innerJoin('todoTag.tag', 'tag')
        .where('todo.spaceId = :spaceId', { spaceId })
        .andWhere('tag.name IN (:...tagNames)', { tagNames: tagArray })
        .getMany();

      const todoIdsWithTags = new Set(todosWithTags.map(t => t.id));
      todos = todos.filter(todo => todoIdsWithTags.has(todo.id));
      total = todos.length;
    }

    // Filter by participant if specified
    if (participantId) {
      todos = todos.filter(todo => 
        todo.participants.some(p => p.userId === participantId)
      );
      total = todos.length;
    }

    return { todos, total };
  }

  async getTodoById(
    todoId: string,
    userId: string,
  ): Promise<Todo> {
    const todo = await this.todoRepository.findOne({
      where: { id: todoId },
      relations: ['creator', 'participants', 'participants.user', 'completedByUser', 'space', 'todoTags', 'todoTags.tag'],
    });

    if (!todo) {
      throw new NotFoundException('Todo not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(todo.spaceId, userId);

    return todo;
  }

  async updateTodo(
    todoId: string,
    userId: string,
    updateTodoDto: UpdateTodoDto,
  ): Promise<Todo> {
    const todo = await this.getTodoById(todoId, userId);

    // Check if user can update this todo (creator or participant)
    const canUpdate = todo.creatorId === userId ||
      todo.participants.some(p => p.userId === userId);

    if (!canUpdate) {
      throw new ForbiddenException('You can only update todos you created or are assigned to');
    }

    // Update basic fields
    if (updateTodoDto.title !== undefined) {
      todo.title = updateTodoDto.title;
    }
    if (updateTodoDto.details !== undefined) {
      todo.details = updateTodoDto.details;
    }

    // Handle status updates - allow all status changes
    if (updateTodoDto.status !== undefined) {
      todo.status = updateTodoDto.status;

      // If marking as completed, set completion info
      if (updateTodoDto.status === TodoStatus.COMPLETED) {
        todo.completedAt = new Date();
        todo.completedByUserId = userId;
      } else {
        // Clear completion info for other statuses
        todo.completedAt = null;
        todo.completedByUserId = null;
      }
    }

    // Handle due date updates and automatic status changes
    if (updateTodoDto.dueDate !== undefined) {
      todo.dueDate = updateTodoDto.dueDate ? new Date(updateTodoDto.dueDate) : null;

      // Auto-update status based on due date presence (only if status wasn't manually set)
      if (updateTodoDto.status === undefined) {
        if (todo.dueDate) {
          // Has due date - set to in_progress
          todo.status = TodoStatus.IN_PROGRESS;
        } else {
          // No due date - set to pending
          todo.status = TodoStatus.PENDING;
        }
      }
    }

    // Handle due time updates
    if (updateTodoDto.dueTime !== undefined) {
      todo.dueTime = updateTodoDto.dueTime;
    }
    if (updateTodoDto.location !== undefined) {
      todo.location = updateTodoDto.location;
    }
    // Note: Tags are now managed through the unified Tag system

    // Update participants if provided
    if (updateTodoDto.participants !== undefined) {
      try {
        // Use a transaction to ensure atomicity for participants
        await this.todoParticipantRepository.manager.transaction(async (transactionalEntityManager) => {
          // Get current participants
          const currentParticipants = await transactionalEntityManager.find(TodoParticipant, {
            where: { todoId },
          });

          // Filter out duplicates by userId from incoming data
          const uniqueParticipants = updateTodoDto.participants.filter((participant, index, self) =>
            self.findIndex(p => p.userId === participant.userId) === index
          );

          // Get target participant user IDs
          const targetUserIds = uniqueParticipants.map(p => p.userId);
          const currentUserIds = currentParticipants.map(p => p.userId);

          // Find participants to remove (current but not in target)
          const participantsToRemove = currentParticipants.filter(p => !targetUserIds.includes(p.userId));

          // Find participants to add (target but not in current)
          const participantsToAdd = uniqueParticipants.filter(p => !currentUserIds.includes(p.userId));

          // Remove participants that are no longer needed
          if (participantsToRemove.length > 0) {
            await transactionalEntityManager.remove(TodoParticipant, participantsToRemove);
            console.log(`🗑️ Removed ${participantsToRemove.length} participants`);
          }

          // Add new participants
          for (const participantData of participantsToAdd) {
            // Get user information to populate userName if not provided
            const user = await this.userRepository.findOne({
              where: { id: participantData.userId },
            });

            if (user) {
              try {
                // Use upsert to handle potential conflicts
                await transactionalEntityManager.upsert(
                  TodoParticipant,
                  {
                    todoId,
                    userId: participantData.userId,
                    userName: participantData.userName || user.displayName || `User ${participantData.userId}`,
                    role: participantData.role || TodoParticipantRole.ASSIGNEE,
                    isNotified: participantData.isNotified !== undefined ? participantData.isNotified : true,
                  },
                  ['todoId', 'userId'] // Conflict target
                );
                console.log(`➕ Added/Updated participant: ${participantData.userId}`);
              } catch (insertError) {
                // Handle constraint conflicts gracefully
                if (insertError.message?.includes('UNIQUE constraint failed')) {
                  console.warn(`⚠️ Participant already exists, skipping: ${todoId} - ${participantData.userId}`);
                } else {
                  console.error(`❌ Error adding participant ${participantData.userId}:`, insertError);
                  throw insertError;
                }
              }
            }
          }
        });
      } catch (error) {
        console.error('❌ Error updating participants:', error);
        // Handle constraint conflicts gracefully
        if (error.message?.includes('UNIQUE constraint failed')) {
          console.warn('⚠️ Participant constraint conflict detected, continuing with other updates...');
        } else {
          throw error;
        }
      }
    }

    // Handle tags update if provided
    if (updateTodoDto.tags !== undefined) {
      try {
        // Validate todoId before proceeding
        if (!todoId) {
          throw new Error('TodoId is required for tag updates');
        }

        console.log(`🏷️ Updating tags for TODO: ${todoId}, tags: ${JSON.stringify(updateTodoDto.tags)}`);

        // Use a simpler approach: get current tags and sync them
        const tags = updateTodoDto.tags && updateTodoDto.tags.length > 0
          ? await this.tagService.getOrCreateTags(todo.spaceId, userId, updateTodoDto.tags)
          : [];

        console.log(`🏷️ Target tags: ${JSON.stringify(tags.map(t => ({ id: t.id, name: t.name })))}`);

        // Get current tag associations
        const currentAssociations = await this.todoTagRepository.find({
          where: { todoId },
          relations: ['tag']
        });

        console.log(`🏷️ Current associations: ${JSON.stringify(currentAssociations.map(a => ({ id: a.id, tagId: a.tagId, tagName: a.tag?.name })))}`);

        // Find tags to remove (current but not in target)
        const currentTagIds = currentAssociations.map(a => a.tagId);
        const targetTagIds = tags.map(t => t.id);
        const tagsToRemove = currentTagIds.filter(id => !targetTagIds.includes(id));
        const tagsToAdd = targetTagIds.filter(id => !currentTagIds.includes(id));

        console.log(`🏷️ Tags to remove: ${JSON.stringify(tagsToRemove)}`);
        console.log(`🏷️ Tags to add: ${JSON.stringify(tagsToAdd)}`);

        // Remove unwanted associations
        if (tagsToRemove.length > 0) {
          const associationsToRemove = currentAssociations.filter(a => tagsToRemove.includes(a.tagId));
          await this.todoTagRepository.remove(associationsToRemove);
          console.log(`🗑️ Removed ${associationsToRemove.length} tag associations`);
        }

        // Add new associations
        if (tagsToAdd.length > 0) {
          console.log(`🔍 Debug: todoId = ${todoId}, type = ${typeof todoId}`);
          console.log(`🔍 Debug: tagsToAdd = ${JSON.stringify(tagsToAdd)}`);

          // Use insert instead of create/save to avoid potential mapping issues
          for (const tagId of tagsToAdd) {
            try {
              await this.todoTagRepository
                .createQueryBuilder()
                .insert()
                .into(TodoTag)
                .values({
                  todoId: todoId,
                  tagId: tagId,
                })
                .execute();
              console.log(`➕ Added tag association: ${todoId} -> ${tagId}`);
            } catch (insertError) {
              // Handle unique constraint violations gracefully
              if (insertError.message?.includes('UNIQUE constraint failed')) {
                console.warn(`⚠️ Tag association already exists: ${todoId} -> ${tagId}`);
              } else {
                console.error(`❌ Error adding tag association:`, insertError);
                throw insertError;
              }
            }
          }
          console.log(`➕ Added ${tagsToAdd.length} tag associations`);
        }

        console.log(`✅ Successfully updated tags for TODO: ${todoId}`);
      } catch (error) {
        console.error('❌ Error updating tags:', error);
        throw error;
      }
    }

    await this.todoRepository.save(todo);

    // Return updated todo with relations
    return this.getTodoById(todoId, userId);
  }

  async completeTodo(
    todoId: string,
    userId: string,
    completeTodoDto?: CompleteTodoDto,
  ): Promise<Todo> {
    const todo = await this.getTodoById(todoId, userId);

    // Check if user can complete this todo (creator or participant)
    const canComplete = todo.creatorId === userId ||
      todo.participants.some(p => p.userId === userId);

    if (!canComplete) {
      throw new ForbiddenException('You can only complete todos you created or are assigned to');
    }

    if (todo.status === TodoStatus.COMPLETED) {
      throw new ForbiddenException('Todo is already completed');
    }

    todo.status = TodoStatus.COMPLETED;
    todo.completedAt = new Date();
    todo.completedByUserId = userId;

    await this.todoRepository.save(todo);

    return this.getTodoById(todoId, userId);
  }

  async deleteAllTodos(spaceId: string, userId: string): Promise<{ deletedTodos: number; deletedParticipants: number }> {
    // Check space membership
    await this.checkSpaceMembership(spaceId, userId);

    // Get all todos in the space
    const todos = await this.todoRepository.find({
      where: { spaceId },
    });

    const todoIds = todos.map(todo => todo.id);

    // Delete all participants first (due to foreign key constraints)
    let deletedParticipants = 0;
    if (todoIds.length > 0) {
      const participantsResult = await this.todoParticipantRepository.delete({
        todoId: In(todoIds),
      });
      deletedParticipants = participantsResult.affected || 0;
    }

    // Delete all todos
    const todosResult = await this.todoRepository.delete({
      spaceId,
    });
    const deletedTodos = todosResult.affected || 0;

    console.log(`🗑️ Deleted ${deletedTodos} TODOs and ${deletedParticipants} participants from space ${spaceId}`);

    return {
      deletedTodos,
      deletedParticipants,
    };
  }

  async deleteAllTodosGlobally(userId: string): Promise<{ deletedTodos: number; deletedParticipants: number }> {
    // Delete all participants first (due to foreign key constraints)
    const participantsResult = await this.todoParticipantRepository
      .createQueryBuilder()
      .delete()
      .execute();
    const deletedParticipants = participantsResult.affected || 0;

    // Delete all todos
    const todosResult = await this.todoRepository
      .createQueryBuilder()
      .delete()
      .execute();
    const deletedTodos = todosResult.affected || 0;

    return {
      deletedTodos,
      deletedParticipants,
    };
  }

  async syncTodoFromMessage(
    spaceId: string,
    messageId: string,
    userId: string,
  ): Promise<Todo> {
    // Check space membership
    await this.checkSpaceMembership(spaceId, userId);

    // Check if todo already exists
    const existingTodo = await this.todoRepository.findOne({
      where: { messageId, spaceId },
      relations: ['participants', 'participants.user', 'todoTags', 'todoTags.tag'],
    });

    if (existingTodo) {
      return existingTodo;
    }

    // Get the message to extract metadata
    const message = await this.messageRepository.findOne({
      where: { id: messageId, spaceId },
    });

    if (!message || message.type !== 'todo') {
      throw new NotFoundException('TODO message not found');
    }

    const todoData = message.metadata as any;
    if (!todoData) {
      throw new BadRequestException('Message has no TODO metadata');
    }

    // Determine initial status based on due date
    const initialStatus = todoData.dueDate ? TodoStatus.IN_PROGRESS : TodoStatus.PENDING;

    // Create the todo record
    const todo = this.todoRepository.create({
      spaceId,
      messageId: message.id,
      creatorId: message.senderId,
      title: todoData.title || message.content,
      details: todoData.details,
      dueDate: todoData.dueDate ? new Date(todoData.dueDate) : null,
      location: todoData.location,
      status: initialStatus,
    });

    const savedTodo = await this.todoRepository.save(todo);

    // Create participants if provided
    if (todoData.participants && todoData.participants.length > 0) {
      const participants = [];

      for (const participantData of todoData.participants) {
        const user = await this.userRepository.findOne({
          where: { id: participantData.userId },
        });

        if (user) {
          participants.push(
            this.todoParticipantRepository.create({
              todoId: savedTodo.id,
              userId: user.id,
              userName: user.displayName,
              role: TodoParticipantRole.ASSIGNEE,
              isNotified: true,
            }),
          );
        }
      }

      if (participants.length > 0) {
        await this.todoParticipantRepository.save(participants);
      }
    }

    // Return the todo with participants and tags
    return this.todoRepository.findOne({
      where: { id: savedTodo.id },
      relations: ['participants', 'participants.user', 'todoTags', 'todoTags.tag'],
    });
  }

  async getTodoByMessageId(
    spaceId: string,
    messageId: string,
    userId: string,
  ): Promise<Todo> {
    // Check space membership
    await this.checkSpaceMembership(spaceId, userId);

    const todo = await this.todoRepository.findOne({
      where: { messageId, spaceId },
      relations: ['participants', 'participants.user', 'todoTags', 'todoTags.tag'],
    });

    if (!todo) {
      throw new NotFoundException('Todo not found for this message');
    }

    return todo;
  }

  async updateTodoStatusByMessageId(
    spaceId: string,
    messageId: string,
    userId: string,
    status: TodoStatus,
  ): Promise<Todo> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    // Find todo by message ID
    const todo = await this.todoRepository.findOne({
      where: { messageId, spaceId },
      relations: ['creator', 'participants', 'participants.user', 'completedByUser', 'todoTags', 'todoTags.tag'],
    });

    if (!todo) {
      throw new NotFoundException('Todo not found for this message');
    }

    // Check if user can update this todo (creator or participant)
    const canUpdate = todo.creatorId === userId ||
      todo.participants.some(p => p.userId === userId);

    if (!canUpdate) {
      throw new ForbiddenException('You can only update todos you created or are assigned to');
    }

    // Update status
    todo.status = status;
    if (status === TodoStatus.COMPLETED) {
      todo.completedAt = new Date();
      todo.completedByUserId = userId;
    } else {
      todo.completedAt = null;
      todo.completedByUserId = null;
    }

    await this.todoRepository.save(todo);

    // Update the message metadata to reflect the new status
    await this.updateMessageMetadata(messageId, todo);

    // Broadcast todo status change via WebSocket
    this.chatGateway.broadcastTodoStatusChanged(spaceId, todo);

    return todo;
  }

  async deleteTodo(
    todoId: string,
    userId: string,
  ): Promise<void> {
    const todo = await this.getTodoById(todoId, userId);

    // Only creator can delete the todo
    if (todo.creatorId !== userId) {
      throw new ForbiddenException('You can only delete todos you created');
    }

    await this.todoRepository.softDelete(todoId);
  }

  private async updateMessageMetadata(messageId: string, todo: Todo): Promise<void> {
    try {
      const message = await this.messageRepository.findOne({
        where: { id: messageId },
      });

      if (message && message.metadata) {
        // Update the metadata with the new todo status
        const updatedMetadata = {
          ...message.metadata,
          status: todo.status,
          completedAt: todo.completedAt?.toISOString() || null,
          completedByUserId: todo.completedByUserId || null,
        };

        message.metadata = updatedMetadata;
        await this.messageRepository.save(message);
      }
    } catch (error) {
      // Log error but don't throw - message metadata update is not critical
      console.error('Failed to update message metadata:', error);
    }
  }

  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }
}