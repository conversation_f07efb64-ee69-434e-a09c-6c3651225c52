import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  ConflictException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TodoService } from './todo.service';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto, CompleteTodoDto } from './dto/update-todo.dto';
import { GetTodosDto } from './dto/get-todos.dto';
import { ApiResponseDto } from '../../common/dto/api-response.dto';

@ApiTags('todos')
@Controller('spaces/:spaceId/todos')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TodoController {
  constructor(private readonly todoService: TodoService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new todo' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({
    status: 201,
    description: 'Todo created successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Not a member of the space',
  })
  async createTodo(
    @Param('spaceId') spaceId: string,
    @Body() createTodoDto: CreateTodoDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const todo = await this.todoService.createTodo(
      spaceId,
      req.user.id,
      createTodoDto,
    );

    return {
      success: true,
      data: todo,
      message: 'Todo created successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get todos in a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({
    status: 200,
    description: 'Todos retrieved successfully',
  })
  async getTodos(
    @Param('spaceId') spaceId: string,
    @Query() getTodosDto: GetTodosDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.todoService.getTodos(
      spaceId,
      req.user.id,
      getTodosDto,
    );

    return {
      success: true,
      data: result,
      message: 'Todos retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  // Note: Space tags are now managed through the unified Tag system
  // Use the Tag API endpoints instead of this deprecated endpoint

  @Get(':todoId')
  @ApiOperation({ summary: 'Get a specific todo' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'todoId', description: 'Todo ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Todo not found',
  })
  async getTodoById(
    @Param('spaceId') spaceId: string,
    @Param('todoId') todoId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const todo = await this.todoService.getTodoById(todoId, req.user.id);

    return {
      success: true,
      data: todo,
      message: 'Todo retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':todoId')
  @ApiOperation({ summary: 'Update a todo' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'todoId', description: 'Todo ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo updated successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Not authorized to update this todo',
  })
  @ApiResponse({
    status: 404,
    description: 'Todo not found',
  })
  async updateTodo(
    @Param('spaceId') spaceId: string,
    @Param('todoId') todoId: string,
    @Body() updateTodoDto: UpdateTodoDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    try {
      const todo = await this.todoService.updateTodo(
        todoId,
        req.user.id,
        updateTodoDto,
      );

      return {
        success: true,
        data: todo,
        message: 'Todo updated successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // Handle constraint conflicts gracefully
      if (error.message?.includes('UNIQUE constraint failed')) {
        throw new ConflictException('Operation conflict detected. Please try again.');
      }
      throw error;
    }
  }

  @Post(':todoId/complete')
  @ApiOperation({ summary: 'Mark a todo as completed' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'todoId', description: 'Todo ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo marked as completed',
  })
  @ApiResponse({
    status: 403,
    description: 'Not authorized to complete this todo',
  })
  async completeTodo(
    @Param('spaceId') spaceId: string,
    @Param('todoId') todoId: string,
    @Body() completeTodoDto: CompleteTodoDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const todo = await this.todoService.completeTodo(
      todoId,
      req.user.id,
      completeTodoDto,
    );

    return {
      success: true,
      data: todo,
      message: 'Todo marked as completed',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('cleanup/all')
  @ApiOperation({ summary: 'Delete all TODO records in space (for testing)' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({
    status: 200,
    description: 'All TODOs deleted successfully',
  })
  async deleteAllTodos(
    @Param('spaceId') spaceId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.todoService.deleteAllTodos(spaceId, req.user.id);

    return {
      success: true,
      data: result,
      message: 'All TODOs deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('cleanup/global')
  @ApiOperation({ summary: 'Delete ALL TODO records globally (for testing)' })
  @ApiResponse({
    status: 200,
    description: 'All TODOs deleted globally',
  })
  async deleteAllTodosGlobally(
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.todoService.deleteAllTodosGlobally(req.user.id);

    return {
      success: true,
      data: result,
      message: 'All TODOs deleted globally',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('message/:messageId/sync')
  @ApiOperation({ summary: 'Sync todo from message metadata to database' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 201,
    description: 'Todo synced successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Message not found',
  })
  async syncTodoFromMessage(
    @Param('spaceId') spaceId: string,
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const todo = await this.todoService.syncTodoFromMessage(
      spaceId,
      messageId,
      req.user.id,
    );

    return {
      success: true,
      data: todo,
      message: 'Todo synced successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('message/:messageId')
  @ApiOperation({ summary: 'Get todo by message ID' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Todo not found for this message',
  })
  @ApiResponse({
    status: 403,
    description: 'Not authorized to access this todo',
  })
  async getTodoByMessageId(
    @Param('spaceId') spaceId: string,
    @Param('messageId') messageId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const todo = await this.todoService.getTodoByMessageId(
      spaceId,
      messageId,
      req.user.id,
    );

    return {
      success: true,
      data: todo,
      message: 'Todo retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put('message/:messageId/status')
  @ApiOperation({ summary: 'Update todo status by message ID' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'messageId', description: 'Message ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo status updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Todo not found for this message',
  })
  @ApiResponse({
    status: 403,
    description: 'Not authorized to update this todo',
  })
  async updateTodoStatusByMessageId(
    @Param('spaceId') spaceId: string,
    @Param('messageId') messageId: string,
    @Body() updateStatusDto: { status: string },
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    // Validate status
    const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(updateStatusDto.status)) {
      throw new Error('Invalid status. Must be one of: ' + validStatuses.join(', '));
    }

    const todo = await this.todoService.updateTodoStatusByMessageId(
      spaceId,
      messageId,
      req.user.id,
      updateStatusDto.status as any,
    );

    return {
      success: true,
      data: todo,
      message: 'Todo status updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':todoId')
  @ApiOperation({ summary: 'Delete a todo' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'todoId', description: 'Todo ID' })
  @ApiResponse({
    status: 200,
    description: 'Todo deleted successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Not authorized to delete this todo',
  })
  async deleteTodo(
    @Param('spaceId') spaceId: string,
    @Param('todoId') todoId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.todoService.deleteTodo(todoId, req.user.id);

    return {
      success: true,
      data: null,
      message: 'Todo deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }
}
